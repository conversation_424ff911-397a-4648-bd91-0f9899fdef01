esphome:
  name: media-screen-escritorio
  friendly_name: Media Screen (Escritório)

esp32:
  board: esp32dev
  framework:
    type: esp-idf

logger:

api:

ota:
  - platform: esphome

wifi:
  ssid: "The Tortured Poets Apartment"
  password: "butdaddyilovehim13"

font:
  - file:
      type: gfonts
      family: Roboto
    id: roboto_large
    size: 24
    bpp: 4
  - file:
      type: gfonts
      family: Roboto
    id: roboto_medium
    size: 16
    bpp: 4
  - file:
      type: gfonts
      family: Roboto
    id: roboto_small
    size: 12
    bpp: 4

# Material Design Icons for buttons
image:
  - file: mdi:play
    id: play_icon
    type: binary
    transparency: chroma_key
    resize: 32x32
  - file: mdi:pause
    id: pause_icon
    type: binary
    transparency: chroma_key
    resize: 32x32
  - file: mdi:skip-previous
    id: prev_icon
    type: binary
    transparency: chroma_key
    resize: 28x28
  - file: mdi:skip-next
    id: next_icon
    type: binary
    transparency: chroma_key
    resize: 28x28
  - file: mdi:volume-minus
    id: vol_down_icon
    type: binary
    transparency: chroma_key
    resize: 20x20
  - file: mdi:volume-plus
    id: vol_up_icon
    type: binary
    transparency: chroma_key
    resize: 20x20

# Modern color scheme (red/blue swapped for your display)
color:
  - id: color_primary
    red: 0.25     # Modern dark gray buttons
    green: 0.28
    blue: 0.32
  - id: color_primary_hover
    red: 0.35     # Lighter on hover/active
    green: 0.38
    blue: 0.42
  - id: color_success
    red: 0.15     # Modern green (playing state)
    green: 0.85
    blue: 0.25
  - id: color_accent
    red: 0.0      # Vibrant blue accent
    green: 0.5
    blue: 1.0
  - id: color_background
    red: 0.05     # Deep dark background
    green: 0.05
    blue: 0.08
  - id: color_surface
    red: 0.08     # Card-like surface
    green: 0.08
    blue: 0.12
  - id: color_surface_variant
    red: 0.12     # Slightly lighter surface
    green: 0.12
    blue: 0.16
  - id: color_text
    red: 0.98     # Pure white text
    green: 0.98
    blue: 0.98
  - id: color_text_secondary
    red: 0.65     # Muted secondary text
    green: 0.65
    blue: 0.68
  - id: color_progress
    red: 0.0      # Bright accent for progress
    green: 0.5
    blue: 1.0
  - id: color_progress_bg
    red: 0.15     # Progress track background
    green: 0.15
    blue: 0.18

globals:
  - id: current_volume
    type: float
    initial_value: '0.5'
  - id: is_playing
    type: bool
    initial_value: 'false'
  - id: play_start_time
    type: unsigned long
    initial_value: '0'
  - id: media_duration_seconds
    type: float
    initial_value: '0.0'
  - id: media_position_at_start
    type: float
    initial_value: '0.0'
  - id: last_activity_time
    type: unsigned long
    initial_value: '0'
  - id: backlight_auto_off_enabled
    type: bool
    initial_value: 'true'

# Get media info from Home Assistant
text_sensor:
  - platform: homeassistant
    id: media_title
    entity_id: media_player.escritorio
    attribute: media_title
    internal: true
    on_value:
      then:
        - lambda: |-
            // Reset position tracking when song changes
            ESP_LOGI("media", "Title changed to: %s", x.c_str());
            id(media_position_at_start) = 0.0;
            id(play_start_time) = millis();

  - platform: homeassistant
    id: media_artist
    entity_id: media_player.escritorio
    attribute: media_artist
    internal: true
    on_value:
      then:
        - lambda: |-
            // Also reset on artist change (additional safety)
            ESP_LOGI("media", "Artist changed to: %s", x.c_str());
            id(media_position_at_start) = 0.0;
            id(play_start_time) = millis();

  - platform: homeassistant
    id: media_album
    entity_id: media_player.escritorio
    attribute: media_album_name
    internal: true

  - platform: homeassistant
    id: media_volume_text
    entity_id: media_player.escritorio
    attribute: volume_level
    internal: true
    on_value:
      then:
        - lambda: |-
            if (!x.empty() && x != "unknown") {
              id(current_volume) = atof(x.c_str());
            }

  # Get duration and position as text (only updates on state change)
  - platform: homeassistant
    id: media_duration_text
    entity_id: media_player.escritorio
    attribute: media_duration
    internal: true
    on_value:
      then:
        - lambda: |-
            if (!x.empty() && x != "unknown") {
              float new_duration = atof(x.c_str());
              // If duration changed significantly, it's likely a new song
              if (abs(new_duration - id(media_duration_seconds)) > 5.0) {
                ESP_LOGI("media", "Duration changed significantly: %f -> %f", id(media_duration_seconds), new_duration);
                id(media_position_at_start) = 0.0;
                id(play_start_time) = millis();
              }
              id(media_duration_seconds) = new_duration;
            }

  - platform: homeassistant
    id: media_position_text
    entity_id: media_player.escritorio
    attribute: media_position
    internal: true
    on_value:
      then:
        - lambda: |-
            if (!x.empty() && x != "unknown") {
              id(media_position_at_start) = atof(x.c_str());
              id(play_start_time) = millis();
            }

  # Track playback state
  - platform: homeassistant
    id: media_state
    entity_id: media_player.escritorio
    internal: true
    on_value:
      then:
        - lambda: |-
            ESP_LOGI("media", "State changed to: %s", x.c_str());
            id(last_activity_time) = millis(); // Update activity time on any state change
            if (x == "playing") {
              id(is_playing) = true;
              id(play_start_time) = millis();
              // Turn backlight on when playing starts
              if (id(backlight_auto_off_enabled)) {
                auto call = id(backlight).turn_on();
                call.perform();
              }
            } else {
              id(is_playing) = false;
            }

# Check for idle timeout every 10 seconds
interval:
  - interval: 10s
    then:
      - lambda: |-
          if (id(backlight_auto_off_enabled) && !id(is_playing)) {
            unsigned long idle_time = millis() - id(last_activity_time);
            // 60000ms = 1 minute
            if (idle_time > 60000) {
              ESP_LOGI("backlight", "Media idle for %lu ms, turning off backlight", idle_time);
              auto call = id(backlight).turn_off();
              call.perform();
            }
          }

script:
  - id: play_pause_speaker
    then:
      - homeassistant.service:
          service: media_player.media_play_pause
          data:
            entity_id: media_player.escritorio

  - id: volume_up
    then:
      - homeassistant.service:
          service: media_player.volume_set
          data:
            entity_id: media_player.escritorio
            volume_level: !lambda |-
              float new_vol = id(current_volume) + 0.1;
              if (new_vol > 1.0) new_vol = 1.0;
              id(current_volume) = new_vol;
              return new_vol;

  - id: volume_down
    then:
      - homeassistant.service:
          service: media_player.volume_set
          data:
            entity_id: media_player.escritorio
            volume_level: !lambda |-
              float new_vol = id(current_volume) - 0.1;
              if (new_vol < 0.0) new_vol = 0.0;
              id(current_volume) = new_vol;
              return new_vol;

  - id: next_track
    then:
      - homeassistant.service:
          service: media_player.media_next_track
          data:
            entity_id: media_player.escritorio

  - id: previous_track
    then:
      - homeassistant.service:
          service: media_player.media_previous_track
          data:
            entity_id: media_player.escritorio

output:
  - platform: ledc
    pin: GPIO21
    id: backlight_pwm

light:
  - platform: monochromatic
    output: backlight_pwm
    name: Display Backlight
    id: backlight
    restore_mode: ALWAYS_ON

spi:
  - id: tft
    clk_pin: GPIO14
    mosi_pin: GPIO13
    miso_pin: GPIO12
  - id: touch
    clk_pin: GPIO25
    mosi_pin: GPIO32
    miso_pin: GPIO39

display:
  - platform: ili9xxx
    model: ILI9342
    spi_id: tft
    cs_pin: GPIO15
    dc_pin: GPIO2
    auto_clear_enabled: false
    invert_colors: false
    color_palette: 8BIT
    rotation: 0
    dimensions: 
      width: 320
      height: 240
    update_interval: 500ms
    lambda: |-
      // Modern dark background
      it.fill(id(color_background));

      // Modern card-like container for song info - taller for better spacing
      it.filled_rectangle(10, 10, 300, 62, id(color_surface));

      // Song info with modern typography hierarchy
      std::string title = id(media_title).state;
      std::string artist = id(media_artist).state;
      std::string album = id(media_album).state;

      // Better text truncation based on available width (280px for text area)
      if (title.length() > 25) title = title.substr(0, 22) + "...";
      if (artist.length() > 35) artist = artist.substr(0, 32) + "...";
      if (album.length() > 35) album = album.substr(0, 32) + "...";

      // Title - larger, prominent
      it.print(160, 25, id(roboto_large), id(color_text), TextAlign::CENTER, title.c_str());
      // Artist - medium prominence (2px more margin)
      it.print(160, 47, id(roboto_medium), id(color_text_secondary), TextAlign::CENTER, artist.c_str());
      // Album - subtle, smaller (2px more margin)
      it.print(160, 62, id(roboto_small), id(color_text_secondary), TextAlign::CENTER, album.c_str());
      
      // Calculate current position (local calculation)
      float current_position = id(media_position_at_start);
      if (id(is_playing) && id(play_start_time) > 0) {
        unsigned long elapsed_ms = millis() - id(play_start_time);
        current_position += (elapsed_ms / 1000.0);
      }

      // Clamp current position to never exceed song duration
      float duration = id(media_duration_seconds);
      if (duration > 0.0 && current_position > duration) {
        current_position = duration;
      }
      
      // Calculate progress (current_position is already clamped)
      float progress = 0.0;
      if (duration > 0.0 && current_position >= 0.0) {
        progress = current_position / duration;
        // Additional safety clamp (should not be needed due to position clamping above)
        if (progress > 1.0) progress = 1.0;
        if (progress < 0.0) progress = 0.0;
      }
      
      // Modern progress bar with better proportions - more spacing from song info
      int bar_x = 20, bar_y = 82, bar_width = 280, bar_height = 6;
      // Background track - subtle
      it.filled_rectangle(bar_x, bar_y, bar_width, bar_height, id(color_progress_bg));

      // Progress fill - vibrant accent color
      int fill_width = (int)(progress * bar_width);
      if (fill_width > 0) {
        it.filled_rectangle(bar_x, bar_y, fill_width, bar_height, id(color_progress));
      }

      // Time display with modern styling
      char time_text[20] = "0:00 / 0:00";
      if (duration > 0.0) {
        int pos_min = (int)(current_position / 60);
        int pos_sec = (int)(current_position) % 60;
        int dur_min = (int)(duration / 60);
        int dur_sec = (int)(duration) % 60;
        sprintf(time_text, "%d:%02d / %d:%02d", pos_min, pos_sec, dur_min, dur_sec);
      }
      it.print(160, 102, id(roboto_small), id(color_text_secondary), TextAlign::CENTER, time_text);
      
      // Modern control buttons - circular design with more spacing
      int button_y = 125;
      int button_size = 48;

      // Previous button - circular
      int prev_x = 60;
      it.filled_circle(prev_x + button_size/2, button_y + button_size/2, button_size/2, id(color_primary));
      it.image(prev_x + button_size/2, button_y + button_size/2, id(prev_icon), ImageAlign::CENTER, id(color_text), COLOR_OFF);

      // Play/Pause button - larger, prominent
      int play_x = 136;
      int play_size = 56;
      auto play_color = id(is_playing) ? id(color_success) : id(color_accent);
      it.filled_circle(play_x + play_size/2, button_y + play_size/2 - 4, play_size/2, play_color);

      // Show play or pause icon based on state
      if (id(is_playing)) {
        it.image(play_x + play_size/2, button_y + play_size/2 - 4, id(pause_icon), ImageAlign::CENTER, id(color_text), COLOR_OFF);
      } else {
        it.image(play_x + play_size/2, button_y + play_size/2 - 4, id(play_icon), ImageAlign::CENTER, id(color_text), COLOR_OFF);
      }

      // Next button - circular
      int next_x = 212;
      it.filled_circle(next_x + button_size/2, button_y + button_size/2, button_size/2, id(color_primary));
      it.image(next_x + button_size/2, button_y + button_size/2, id(next_icon), ImageAlign::CENTER, id(color_text), COLOR_OFF);
      
      // Modern volume section with more spacing
      int vol_y = 195;

      // Volume bar - sleek and minimal
      int vol_bar_x = 70, vol_bar_w = 180, vol_bar_h = 4;
      it.filled_rectangle(vol_bar_x, vol_y, vol_bar_w, vol_bar_h, id(color_progress_bg));
      int vol_fill_w = (int)(vol_bar_w * id(current_volume));
      if (vol_fill_w > 0) {
        it.filled_rectangle(vol_bar_x, vol_y, vol_fill_w, vol_bar_h, id(color_accent));
      }

      // Volume buttons - smaller, circular, lowered for better alignment
      int vol_button_size = 32;
      int vol_button_y = vol_y + 2 - vol_button_size/2 + 15;
      int vol_down_x = 25;
      it.filled_circle(vol_down_x + vol_button_size/2, vol_button_y, vol_button_size/2, id(color_surface_variant));
      it.image(vol_down_x + vol_button_size/2, vol_button_y, id(vol_down_icon), ImageAlign::CENTER, id(color_text), COLOR_OFF);

      int vol_up_x = 263;
      it.filled_circle(vol_up_x + vol_button_size/2, vol_button_y, vol_button_size/2, id(color_surface_variant));
      it.image(vol_up_x + vol_button_size/2, vol_button_y, id(vol_up_icon), ImageAlign::CENTER, id(color_text), COLOR_OFF);

      // Volume percentage - subtle
      char vol_text[10];
      sprintf(vol_text, "%d%%", (int)(id(current_volume) * 100));
      it.print(160, 218, id(roboto_small), id(color_text_secondary), TextAlign::CENTER, vol_text);

touchscreen:
  platform: xpt2046
  id: my_touchscreen
  spi_id: touch
  cs_pin: GPIO33
  interrupt_pin: GPIO36
  calibration:
    x_min: 364
    x_max: 3778
    y_min: 388
    y_max: 3757
  transform:
    swap_xy: true
    mirror_x: true  # Fixed X-axis mirroring
  on_touch:
    lambda: |-
      int touch_x = touch.x;
      int touch_y = 240 - touch.y;

      ESP_LOGI("touch", "Touch at: %d, %d", touch_x, touch_y);

      // Update activity time and turn on backlight on any touch
      id(last_activity_time) = millis();
      if (id(backlight_auto_off_enabled)) {
        auto call = id(backlight).turn_on();
        call.perform();
      }
      
      // Play/Pause button - larger circular (136,121,56,56)
      if (touch_x >= 136 && touch_x <= 192 && touch_y >= 121 && touch_y <= 177) {
        ESP_LOGI("touch", "Play/Pause pressed");
        id(play_pause_speaker).execute();
      }

      // Previous track button - circular (60,125,48,48)
      else if (touch_x >= 60 && touch_x <= 108 && touch_y >= 125 && touch_y <= 173) {
        ESP_LOGI("touch", "Previous track pressed");
        id(previous_track).execute();
      }

      // Next track button - circular (212,125,48,48)
      else if (touch_x >= 212 && touch_x <= 260 && touch_y >= 125 && touch_y <= 173) {
        ESP_LOGI("touch", "Next track pressed");
        id(next_track).execute();
      }
      
      // Volume down button - circular (25,194,32,32)
      else if (touch_x >= 25 && touch_x <= 57 && touch_y >= 194 && touch_y <= 228) {
        ESP_LOGI("touch", "Volume down pressed");
        id(volume_down).execute();
      }

      // Volume up button - circular (263,194,32,32)
      else if (touch_x >= 263 && touch_x <= 295 && touch_y >= 194 && touch_y <= 228) {
        ESP_LOGI("touch", "Volume up pressed");
        id(volume_up).execute();
      }

# Expose auto-off control to Home Assistant
switch:
  - platform: template
    name: "Screen Auto-Off"
    id: auto_off_switch
    restore_mode: RESTORE_DEFAULT_ON
    turn_on_action:
      - lambda: |-
          id(backlight_auto_off_enabled) = true;
          ESP_LOGI("backlight", "Auto-off enabled via switch");
    turn_off_action:
      - lambda: |-
          id(backlight_auto_off_enabled) = false;
          ESP_LOGI("backlight", "Auto-off disabled via switch");
          // Turn backlight on when auto-off is disabled
          auto call = id(backlight).turn_on();
          call.perform();
    lambda: |-
      return id(backlight_auto_off_enabled);

sensor:
  - platform: wifi_signal # Reports the Wi-Fi signal strength in %
    name: "Wi-Fi Signal"
    filters:
      - lambda: return min(max(2 * (x + 100.0), 0.0), 100.0);
    unit_of_measurement: "%"
    id: wifi_signal_pct
    update_interval: 60s
    entity_category: "diagnostic"